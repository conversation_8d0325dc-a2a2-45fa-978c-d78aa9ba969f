<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - ' : ''; ?><?php echo get_setting('company_name', 'Construction Company'); ?></title>
    <meta name="description" content="<?php echo isset($page_description) ? $page_description : 'Professional construction services including civil engineering, groundworks, RC frames, basements, and hard landscaping.'; ?>">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/images/favicon.ico">
    
    <!-- CSS -->
    <link rel="stylesheet" href="/assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="/assets/css/fontawesome.min.css">
    <link rel="stylesheet" href="/assets/css/style.css">
    <link rel="stylesheet" href="/assets/css/responsive.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Oswald:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo isset($page_title) ? $page_title : get_setting('company_name', 'Construction Company'); ?>">
    <meta property="og:description" content="<?php echo isset($page_description) ? $page_description : 'Professional construction services'; ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo 'http://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']; ?>">
    <meta property="og:image" content="/assets/images/og-image.jpg">
    
    <!-- Schema.org markup -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "ConstructionCompany",
        "name": "<?php echo get_setting('company_name', 'Construction Company'); ?>",
        "telephone": "<?php echo get_setting('company_phone', ''); ?>",
        "email": "<?php echo get_setting('company_email', ''); ?>",
        "address": {
            "@type": "PostalAddress",
            "streetAddress": "<?php echo get_setting('company_address', ''); ?>"
        },
        "url": "<?php echo 'http://' . $_SERVER['HTTP_HOST']; ?>"
    }
    </script>
</head>
<body>
    <!-- Top Bar -->
    <div class="top-bar">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="contact-info">
                        <a href="tel:<?php echo get_setting('company_phone', ''); ?>">
                            <i class="fas fa-phone"></i> <?php echo get_setting('company_phone', ''); ?>
                        </a>
                        <a href="mailto:<?php echo get_setting('company_email', ''); ?>">
                            <i class="fas fa-envelope"></i> <?php echo get_setting('company_email', ''); ?>
                        </a>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="social-links text-end">
                        <?php if (get_setting('facebook_url')): ?>
                        <a href="<?php echo get_setting('facebook_url'); ?>" target="_blank"><i class="fab fa-facebook-f"></i></a>
                        <?php endif; ?>
                        <?php if (get_setting('instagram_url')): ?>
                        <a href="<?php echo get_setting('instagram_url'); ?>" target="_blank"><i class="fab fa-instagram"></i></a>
                        <?php endif; ?>
                        <?php if (get_setting('youtube_url')): ?>
                        <a href="<?php echo get_setting('youtube_url'); ?>" target="_blank"><i class="fab fa-youtube"></i></a>
                        <?php endif; ?>
                        <?php if (get_setting('linkedin_url')): ?>
                        <a href="<?php echo get_setting('linkedin_url'); ?>" target="_blank"><i class="fab fa-linkedin-in"></i></a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Header -->
    <header class="header">
        <nav class="navbar navbar-expand-lg navbar-light">
            <div class="container">
                <a class="navbar-brand" href="/">
                    <img src="/assets/images/logo.png" alt="<?php echo get_setting('company_name', 'Construction Company'); ?>" class="logo">
                </a>
                
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav ms-auto">
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($_GET['page'] ?? 'home') == 'home' ? 'active' : ''; ?>" href="/">Home</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($_GET['page'] ?? '') == 'about' ? 'active' : ''; ?>" href="/?page=about">About Us</a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle <?php echo ($_GET['page'] ?? '') == 'services' ? 'active' : ''; ?>" href="/?page=services" id="servicesDropdown" role="button" data-bs-toggle="dropdown">
                                Our Services
                            </a>
                            <ul class="dropdown-menu">
                                <?php
                                $stmt = $db->prepare("SELECT title, slug FROM services WHERE is_active = 1 ORDER BY sort_order");
                                $stmt->execute();
                                $services = $stmt->fetchAll();
                                foreach ($services as $service):
                                ?>
                                <li><a class="dropdown-item" href="/?page=service&slug=<?php echo $service['slug']; ?>"><?php echo $service['title']; ?></a></li>
                                <?php endforeach; ?>
                            </ul>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle <?php echo ($_GET['page'] ?? '') == 'projects' ? 'active' : ''; ?>" href="/?page=projects" id="projectsDropdown" role="button" data-bs-toggle="dropdown">
                                Our Projects
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="/?page=projects&type=completed">Completed Projects</a></li>
                                <li><a class="dropdown-item" href="/?page=projects&type=ongoing">Ongoing Projects</a></li>
                            </ul>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($_GET['page'] ?? '') == 'media' ? 'active' : ''; ?>" href="/?page=media">Media</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($_GET['page'] ?? '') == 'contact' ? 'active' : ''; ?>" href="/?page=contact">Contact Us</a>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <main class="main-content"><?php // Content will be loaded here ?>
