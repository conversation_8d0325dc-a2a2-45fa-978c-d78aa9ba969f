<?php
/**
 * Construction Company Website Setup Script
 * This script helps with the initial setup and configuration
 */

// Prevent running in production
if (file_exists('PRODUCTION')) {
    die('Setup script is disabled in production mode.');
}

$step = $_GET['step'] ?? 1;
$errors = [];
$success = [];

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    switch ($step) {
        case 2:
            $result = setupDatabase();
            if ($result['success']) {
                $success[] = $result['message'];
                $step = 3;
            } else {
                $errors[] = $result['message'];
            }
            break;
        case 3:
            $result = createAdminUser();
            if ($result['success']) {
                $success[] = $result['message'];
                $step = 4;
            } else {
                $errors[] = $result['message'];
            }
            break;
        case 4:
            $result = configureSettings();
            if ($result['success']) {
                $success[] = $result['message'];
                $step = 5;
            } else {
                $errors[] = $result['message'];
            }
            break;
    }
}

function checkRequirements() {
    $requirements = [
        'PHP Version >= 8.0' => version_compare(PHP_VERSION, '8.0.0', '>='),
        'PDO Extension' => extension_loaded('pdo'),
        'PDO MySQL Extension' => extension_loaded('pdo_mysql'),
        'GD Extension' => extension_loaded('gd'),
        'JSON Extension' => extension_loaded('json'),
        'Uploads Directory Writable' => is_writable('uploads') || mkdir('uploads', 0755, true),
        'Assets Directory Writable' => is_writable('assets/images') || mkdir('assets/images', 0755, true),
    ];
    
    return $requirements;
}

function setupDatabase() {
    $host = $_POST['db_host'] ?? 'localhost';
    $name = $_POST['db_name'] ?? 'construction_company';
    $user = $_POST['db_user'] ?? 'root';
    $pass = $_POST['db_pass'] ?? '';
    
    try {
        // Test connection
        $pdo = new PDO("mysql:host=$host", $user, $pass);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Create database if it doesn't exist
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `$name`");
        $pdo->exec("USE `$name`");
        
        // Read and execute schema
        $schema = file_get_contents('database/schema.sql');
        $statements = explode(';', $schema);
        
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement)) {
                $pdo->exec($statement);
            }
        }
        
        // Update config file
        $config = "<?php\n";
        $config .= "define('DB_HOST', '$host');\n";
        $config .= "define('DB_NAME', '$name');\n";
        $config .= "define('DB_USER', '$user');\n";
        $config .= "define('DB_PASS', '$pass');\n";
        
        file_put_contents('config/database.php', $config);
        
        return ['success' => true, 'message' => 'Database setup completed successfully!'];
        
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Database setup failed: ' . $e->getMessage()];
    }
}

function createAdminUser() {
    $username = $_POST['admin_username'] ?? '';
    $email = $_POST['admin_email'] ?? '';
    $password = $_POST['admin_password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    
    if (empty($username) || empty($email) || empty($password)) {
        return ['success' => false, 'message' => 'All fields are required.'];
    }
    
    if ($password !== $confirm_password) {
        return ['success' => false, 'message' => 'Passwords do not match.'];
    }
    
    if (strlen($password) < 6) {
        return ['success' => false, 'message' => 'Password must be at least 6 characters long.'];
    }
    
    try {
        require_once 'config/database.php';
        $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
        
        $stmt = $pdo->prepare("UPDATE users SET username = ?, email = ?, password = ? WHERE id = 1");
        $stmt->execute([$username, $email, $hashedPassword]);
        
        return ['success' => true, 'message' => 'Admin user created successfully!'];
        
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Failed to create admin user: ' . $e->getMessage()];
    }
}

function configureSettings() {
    $settings = [
        'company_name' => $_POST['company_name'] ?? '',
        'company_phone' => $_POST['company_phone'] ?? '',
        'company_email' => $_POST['company_email'] ?? '',
        'company_address' => $_POST['company_address'] ?? '',
    ];
    
    try {
        require_once 'config/database.php';
        $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        foreach ($settings as $key => $value) {
            if (!empty($value)) {
                $stmt = $pdo->prepare("UPDATE settings SET setting_value = ? WHERE setting_key = ?");
                $stmt->execute([$value, $key]);
            }
        }
        
        return ['success' => true, 'message' => 'Settings configured successfully!'];
        
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Failed to configure settings: ' . $e->getMessage()];
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Construction Company Setup</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background: #f8f9fa; }
        .setup-container { max-width: 800px; margin: 50px auto; }
        .setup-card { background: white; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.1); }
        .setup-header { background: linear-gradient(45deg, #ff6b35, #2c3e50); color: white; padding: 30px; border-radius: 10px 10px 0 0; }
        .step-indicator { display: flex; justify-content: center; margin: 30px 0; }
        .step { width: 40px; height: 40px; border-radius: 50%; background: #dee2e6; color: #6c757d; display: flex; align-items: center; justify-content: center; margin: 0 10px; }
        .step.active { background: #ff6b35; color: white; }
        .step.completed { background: #28a745; color: white; }
        .requirement { display: flex; align-items: center; padding: 10px 0; border-bottom: 1px solid #eee; }
        .requirement:last-child { border-bottom: none; }
        .requirement.pass { color: #28a745; }
        .requirement.fail { color: #dc3545; }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="setup-card">
            <div class="setup-header text-center">
                <h1><i class="fas fa-tools"></i> Construction Company Setup</h1>
                <p class="mb-0">Welcome! Let's set up your construction company website.</p>
            </div>
            
            <div class="step-indicator">
                <div class="step <?php echo $step >= 1 ? 'active' : ''; ?>">1</div>
                <div class="step <?php echo $step >= 2 ? ($step > 2 ? 'completed' : 'active') : ''; ?>">2</div>
                <div class="step <?php echo $step >= 3 ? ($step > 3 ? 'completed' : 'active') : ''; ?>">3</div>
                <div class="step <?php echo $step >= 4 ? ($step > 4 ? 'completed' : 'active') : ''; ?>">4</div>
                <div class="step <?php echo $step >= 5 ? 'active' : ''; ?>">5</div>
            </div>
            
            <div class="p-4">
                <?php if (!empty($errors)): ?>
                    <div class="alert alert-danger">
                        <?php foreach ($errors as $error): ?>
                            <div><i class="fas fa-exclamation-triangle"></i> <?php echo $error; ?></div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
                
                <?php if (!empty($success)): ?>
                    <div class="alert alert-success">
                        <?php foreach ($success as $message): ?>
                            <div><i class="fas fa-check-circle"></i> <?php echo $message; ?></div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($step == 1): ?>
                    <h3>Step 1: System Requirements</h3>
                    <p>Let's check if your system meets the requirements:</p>
                    
                    <div class="requirements">
                        <?php
                        $requirements = checkRequirements();
                        $allPassed = true;
                        foreach ($requirements as $requirement => $passed):
                            if (!$passed) $allPassed = false;
                        ?>
                            <div class="requirement <?php echo $passed ? 'pass' : 'fail'; ?>">
                                <i class="fas <?php echo $passed ? 'fa-check-circle' : 'fa-times-circle'; ?> me-3"></i>
                                <span><?php echo $requirement; ?></span>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <div class="text-center mt-4">
                        <?php if ($allPassed): ?>
                            <a href="?step=2" class="btn btn-primary btn-lg">
                                Continue to Database Setup <i class="fas fa-arrow-right"></i>
                            </a>
                        <?php else: ?>
                            <div class="alert alert-warning">
                                Please fix the failed requirements before continuing.
                            </div>
                            <a href="?step=1" class="btn btn-secondary">Recheck Requirements</a>
                        <?php endif; ?>
                    </div>
                
                <?php elseif ($step == 2): ?>
                    <h3>Step 2: Database Configuration</h3>
                    <p>Enter your database connection details:</p>
                    
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Database Host</label>
                                <input type="text" class="form-control" name="db_host" value="localhost" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Database Name</label>
                                <input type="text" class="form-control" name="db_name" value="construction_company" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Database Username</label>
                                <input type="text" class="form-control" name="db_user" value="root" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Database Password</label>
                                <input type="password" class="form-control" name="db_pass">
                            </div>
                        </div>
                        <div class="text-center">
                            <button type="submit" class="btn btn-primary btn-lg">
                                Setup Database <i class="fas fa-database"></i>
                            </button>
                        </div>
                    </form>
                
                <?php elseif ($step == 3): ?>
                    <h3>Step 3: Create Admin User</h3>
                    <p>Create your admin account:</p>
                    
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Username</label>
                                <input type="text" class="form-control" name="admin_username" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Email</label>
                                <input type="email" class="form-control" name="admin_email" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Password</label>
                                <input type="password" class="form-control" name="admin_password" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Confirm Password</label>
                                <input type="password" class="form-control" name="confirm_password" required>
                            </div>
                        </div>
                        <div class="text-center">
                            <button type="submit" class="btn btn-primary btn-lg">
                                Create Admin User <i class="fas fa-user-plus"></i>
                            </button>
                        </div>
                    </form>
                
                <?php elseif ($step == 4): ?>
                    <h3>Step 4: Company Settings</h3>
                    <p>Configure your company information:</p>
                    
                    <form method="POST">
                        <div class="mb-3">
                            <label class="form-label">Company Name</label>
                            <input type="text" class="form-control" name="company_name" required>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Phone Number</label>
                                <input type="text" class="form-control" name="company_phone">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Email Address</label>
                                <input type="email" class="form-control" name="company_email">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Company Address</label>
                            <textarea class="form-control" name="company_address" rows="3"></textarea>
                        </div>
                        <div class="text-center">
                            <button type="submit" class="btn btn-primary btn-lg">
                                Save Settings <i class="fas fa-save"></i>
                            </button>
                        </div>
                    </form>
                
                <?php elseif ($step == 5): ?>
                    <div class="text-center">
                        <h3><i class="fas fa-check-circle text-success"></i> Setup Complete!</h3>
                        <p class="lead">Your construction company website is now ready to use.</p>
                        
                        <div class="row mt-4">
                            <div class="col-md-6 mb-3">
                                <div class="card">
                                    <div class="card-body">
                                        <h5><i class="fas fa-globe"></i> Website</h5>
                                        <p>Visit your website</p>
                                        <a href="/" class="btn btn-outline-primary">View Website</a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="card">
                                    <div class="card-body">
                                        <h5><i class="fas fa-cog"></i> Admin Panel</h5>
                                        <p>Manage your content</p>
                                        <a href="/admin/" class="btn btn-outline-primary">Admin Panel</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="alert alert-info mt-4">
                            <strong>Important:</strong> For security reasons, please delete this setup.php file after setup is complete.
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</body>
</html>
