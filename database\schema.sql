-- Construction Company Database Schema
-- Create database
CREATE DATABASE IF NOT EXISTS construction_company;
USE construction_company;

-- Users table for admin authentication
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('admin', 'manager') DEFAULT 'admin',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);

-- Services table
CREATE TABLE IF NOT EXISTS services (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(100) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    short_description VARCHAR(255),
    image VARCHAR(255),
    icon VARCHAR(100),
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Projects table
CREATE TABLE IF NOT EXISTS projects (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(150) NOT NULL,
    slug VARCHAR(150) UNIQUE NOT NULL,
    description TEXT,
    short_description VARCHAR(255),
    client_name VARCHAR(100),
    location VARCHAR(150),
    project_type ENUM('completed', 'ongoing') DEFAULT 'completed',
    start_date DATE,
    end_date DATE,
    featured_image VARCHAR(255),
    gallery_images JSON,
    status ENUM('planning', 'in_progress', 'completed', 'on_hold') DEFAULT 'completed',
    is_featured BOOLEAN DEFAULT FALSE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Media table for gallery management
CREATE TABLE IF NOT EXISTS media (
    id INT AUTO_INCREMENT PRIMARY KEY,
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_type ENUM('image', 'video') DEFAULT 'image',
    mime_type VARCHAR(100),
    file_size INT,
    alt_text VARCHAR(255),
    caption TEXT,
    category VARCHAR(50),
    is_featured BOOLEAN DEFAULT FALSE,
    uploaded_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (uploaded_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Contact submissions table
CREATE TABLE IF NOT EXISTS contact_submissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    subject VARCHAR(150),
    message TEXT NOT NULL,
    status ENUM('new', 'read', 'replied', 'archived') DEFAULT 'new',
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Analytics table for tracking
CREATE TABLE IF NOT EXISTS analytics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    page_url VARCHAR(255) NOT NULL,
    visitor_ip VARCHAR(45),
    user_agent TEXT,
    referrer VARCHAR(255),
    visit_date DATE NOT NULL,
    visit_time TIME NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_date (visit_date),
    INDEX idx_page (page_url)
);

-- Company settings table
CREATE TABLE IF NOT EXISTS settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type ENUM('text', 'textarea', 'number', 'boolean', 'json') DEFAULT 'text',
    description VARCHAR(255),
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert default admin user (password: admin123)
INSERT IGNORE INTO users (username, email, password, role) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin');

-- Insert default services
INSERT IGNORE INTO services (title, slug, description, short_description, icon, sort_order) VALUES
('Civil Engineering', 'civil-engineering', 'Comprehensive civil engineering solutions for infrastructure development and construction projects.', 'Professional civil engineering services', 'fas fa-hard-hat', 1),
('Groundworks', 'groundworks', 'Expert groundwork services including excavation, foundation preparation, and site development.', 'Professional groundwork solutions', 'fas fa-tools', 2),
('RC Frames', 'rc-frames', 'Reinforced concrete frame construction for residential and commercial buildings.', 'Reinforced concrete construction', 'fas fa-building', 3),
('Basements', 'basements', 'Basement construction and waterproofing services for residential and commercial properties.', 'Basement construction specialists', 'fas fa-home', 4),
('Hard Landscaping', 'hard-landscaping', 'Professional hard landscaping services including paving, retaining walls, and outdoor structures.', 'Hard landscaping solutions', 'fas fa-tree', 5);

-- Insert default settings
INSERT IGNORE INTO settings (setting_key, setting_value, setting_type, description) VALUES
('company_name', 'Construction Company Ltd', 'text', 'Company name'),
('company_phone', '0208 914 7883', 'text', 'Main phone number'),
('company_mobile', '078 8292 3621', 'text', 'Mobile phone number'),
('company_email', '<EMAIL>', 'text', 'Main email address'),
('company_address', '662 High Road North Finchley, London N12 0NL', 'textarea', 'Company address'),
('facebook_url', '#', 'text', 'Facebook page URL'),
('instagram_url', '#', 'text', 'Instagram page URL'),
('youtube_url', '#', 'text', 'YouTube channel URL'),
('linkedin_url', '#', 'text', 'LinkedIn page URL');
