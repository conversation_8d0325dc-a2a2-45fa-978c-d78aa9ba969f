{"name": "ConstructionCompanyAdmin", "version": "1.0.0", "description": "Construction Company Admin Mobile App", "main": "index.js", "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint .", "build:android": "cd android && ./gradlew assembleRelease", "build:ios": "cd ios && xcodebuild -workspace ConstructionCompanyAdmin.xcworkspace -scheme ConstructionCompanyAdmin -configuration Release -destination generic/platform=iOS -archivePath ConstructionCompanyAdmin.xcarchive archive"}, "dependencies": {"@react-native-async-storage/async-storage": "^1.19.3", "@react-native-community/netinfo": "^9.4.1", "@react-navigation/bottom-tabs": "^6.5.8", "@react-navigation/native": "^6.1.7", "@react-navigation/stack": "^6.3.17", "react": "18.2.0", "react-native": "0.72.4", "react-native-chart-kit": "^6.12.0", "react-native-document-picker": "^9.0.1", "react-native-gesture-handler": "^2.12.1", "react-native-image-picker": "^5.6.0", "react-native-paper": "^5.10.4", "react-native-push-notification": "^8.1.1", "react-native-reanimated": "^3.5.4", "react-native-safe-area-context": "^4.7.2", "react-native-screens": "^3.25.0", "react-native-svg": "^13.14.0", "react-native-vector-icons": "^10.0.0", "react-query": "^3.39.3", "axios": "^1.5.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.72.2", "@react-native/metro-config": "^0.72.11", "@tsconfig/react-native": "^3.0.0", "@types/react": "^18.0.24", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.76.8", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "4.8.4"}, "engines": {"node": ">=16"}, "keywords": ["react-native", "construction", "admin", "mobile", "cms"], "author": "Construction Company", "license": "MIT"}