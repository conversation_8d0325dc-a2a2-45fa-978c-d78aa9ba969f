<?php
session_start();

// Include core files
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!is_logged_in()) {
    header('Location: login.php');
    exit();
}

// Initialize database connection
$database = new Database();
$db = $database->getConnection();

// Get dashboard statistics
$stats = [];

// Total projects
$stmt = $db->query("SELECT COUNT(*) as total FROM projects");
$stats['total_projects'] = $stmt->fetch()['total'];

// Completed projects
$stmt = $db->query("SELECT COUNT(*) as total FROM projects WHERE status = 'completed'");
$stats['completed_projects'] = $stmt->fetch()['total'];

// Ongoing projects
$stmt = $db->query("SELECT COUNT(*) as total FROM projects WHERE status IN ('planning', 'in_progress')");
$stats['ongoing_projects'] = $stmt->fetch()['total'];

// Total services
$stmt = $db->query("SELECT COUNT(*) as total FROM services WHERE is_active = 1");
$stats['active_services'] = $stmt->fetch()['total'];

// Contact submissions (new)
$stmt = $db->query("SELECT COUNT(*) as total FROM contact_submissions WHERE status = 'new'");
$stats['new_contacts'] = $stmt->fetch()['total'];

// Recent contact submissions
$stmt = $db->prepare("SELECT * FROM contact_submissions ORDER BY created_at DESC LIMIT 5");
$stmt->execute();
$recent_contacts = $stmt->fetchAll();

// Recent projects
$stmt = $db->prepare("SELECT * FROM projects ORDER BY created_at DESC LIMIT 5");
$stmt->execute();
$recent_projects = $stmt->fetchAll();

// Analytics data for the last 30 days
$stmt = $db->prepare("
    SELECT 
        visit_date,
        COUNT(*) as visits 
    FROM analytics 
    WHERE visit_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
    GROUP BY visit_date 
    ORDER BY visit_date
");
$stmt->execute();
$analytics_data = $stmt->fetchAll();

$page_title = "Dashboard";
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <?php include 'includes/sidebar.php'; ?>
        
        <!-- Main Content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Dashboard</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-sm btn-outline-secondary">Share</button>
                        <button type="button" class="btn btn-sm btn-outline-secondary">Export</button>
                    </div>
                    <button type="button" class="btn btn-sm btn-primary dropdown-toggle">
                        <i class="fas fa-calendar"></i>
                        This week
                    </button>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        Total Projects
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo $stats['total_projects']; ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-building fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        Completed Projects
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo $stats['completed_projects']; ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-info shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        Ongoing Projects
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo $stats['ongoing_projects']; ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-cog fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-warning shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        New Messages
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo $stats['new_contacts']; ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-envelope fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts Row -->
            <div class="row mb-4">
                <div class="col-xl-8 col-lg-7">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                            <h6 class="m-0 font-weight-bold text-primary">Website Analytics</h6>
                        </div>
                        <div class="card-body">
                            <div class="chart-area">
                                <canvas id="analyticsChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-4 col-lg-5">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                            <h6 class="m-0 font-weight-bold text-primary">Project Status</h6>
                        </div>
                        <div class="card-body">
                            <div class="chart-pie pt-4 pb-2">
                                <canvas id="projectStatusChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Content Row -->
            <div class="row">
                <!-- Recent Projects -->
                <div class="col-lg-6 mb-4">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Recent Projects</h6>
                        </div>
                        <div class="card-body">
                            <?php if (empty($recent_projects)): ?>
                                <p class="text-muted">No projects found.</p>
                            <?php else: ?>
                                <?php foreach ($recent_projects as $project): ?>
                                <div class="d-flex align-items-center mb-3">
                                    <div class="me-3">
                                        <?php if ($project['featured_image']): ?>
                                            <img src="../assets/images/projects/<?php echo $project['featured_image']; ?>" 
                                                 alt="<?php echo htmlspecialchars($project['title']); ?>" 
                                                 class="rounded" style="width: 50px; height: 50px; object-fit: cover;">
                                        <?php else: ?>
                                            <div class="bg-secondary rounded d-flex align-items-center justify-content-center" 
                                                 style="width: 50px; height: 50px;">
                                                <i class="fas fa-building text-white"></i>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1"><?php echo htmlspecialchars($project['title']); ?></h6>
                                        <small class="text-muted">
                                            <?php echo ucfirst($project['status']); ?> • 
                                            <?php echo format_date($project['created_at']); ?>
                                        </small>
                                    </div>
                                    <div>
                                        <a href="projects.php?action=edit&id=<?php echo $project['id']; ?>" 
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                                <div class="text-center">
                                    <a href="projects.php" class="btn btn-primary btn-sm">View All Projects</a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Recent Messages -->
                <div class="col-lg-6 mb-4">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Recent Messages</h6>
                        </div>
                        <div class="card-body">
                            <?php if (empty($recent_contacts)): ?>
                                <p class="text-muted">No messages found.</p>
                            <?php else: ?>
                                <?php foreach ($recent_contacts as $contact): ?>
                                <div class="d-flex align-items-center mb-3">
                                    <div class="me-3">
                                        <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center" 
                                             style="width: 40px; height: 40px;">
                                            <span class="text-white font-weight-bold">
                                                <?php echo strtoupper(substr($contact['name'], 0, 1)); ?>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1"><?php echo htmlspecialchars($contact['name']); ?></h6>
                                        <p class="mb-1 small"><?php echo truncate_text($contact['message'], 50); ?></p>
                                        <small class="text-muted"><?php echo format_date($contact['created_at']); ?></small>
                                    </div>
                                    <div>
                                        <span class="badge bg-<?php echo $contact['status'] == 'new' ? 'warning' : 'secondary'; ?>">
                                            <?php echo ucfirst($contact['status']); ?>
                                        </span>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                                <div class="text-center">
                                    <a href="contacts.php" class="btn btn-primary btn-sm">View All Messages</a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
// Analytics Chart
const analyticsCtx = document.getElementById('analyticsChart').getContext('2d');
const analyticsChart = new Chart(analyticsCtx, {
    type: 'line',
    data: {
        labels: [<?php echo "'" . implode("','", array_column($analytics_data, 'visit_date')) . "'"; ?>],
        datasets: [{
            label: 'Website Visits',
            data: [<?php echo implode(',', array_column($analytics_data, 'visits')); ?>],
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Project Status Chart
const statusCtx = document.getElementById('projectStatusChart').getContext('2d');
const statusChart = new Chart(statusCtx, {
    type: 'doughnut',
    data: {
        labels: ['Completed', 'In Progress', 'Planning'],
        datasets: [{
            data: [
                <?php echo $stats['completed_projects']; ?>,
                <?php echo $stats['ongoing_projects']; ?>,
                <?php echo $stats['total_projects'] - $stats['completed_projects'] - $stats['ongoing_projects']; ?>
            ],
            backgroundColor: [
                '#28a745',
                '#17a2b8',
                '#ffc107'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false
    }
});
</script>

<?php include 'includes/footer.php'; ?>
