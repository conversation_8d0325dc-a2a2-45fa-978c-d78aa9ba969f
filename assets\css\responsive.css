/* Responsive Design for Construction Company Website */

/* Large Desktop (1200px and up) */
@media (min-width: 1200px) {
    .container {
        max-width: 1140px;
    }
    
    .hero-title {
        font-size: 5rem;
    }
    
    .section-title {
        font-size: 3rem;
    }
}

/* Desktop (992px to 1199px) */
@media (max-width: 1199.98px) {
    .hero-title {
        font-size: 4rem;
    }
    
    .section-title {
        font-size: 2.5rem;
    }
    
    .navbar-nav .nav-link {
        margin: 0 10px;
    }
}

/* Tablet (768px to 991px) */
@media (max-width: 991.98px) {
    /* Navigation */
    .navbar-collapse {
        background: var(--white-color);
        padding: 20px;
        margin-top: 15px;
        border-radius: var(--border-radius);
        box-shadow: var(--box-shadow);
    }
    
    .navbar-nav .nav-link {
        margin: 5px 0;
        padding: 15px 0;
        border-bottom: 1px solid var(--border-color);
    }
    
    .navbar-nav .nav-link:last-child {
        border-bottom: none;
    }
    
    /* Hero Section */
    .hero-title {
        font-size: 3rem;
    }
    
    .hero-subtitle {
        font-size: 1.3rem;
    }
    
    .hero-description {
        font-size: 1.1rem;
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .hero-nav {
        bottom: 20px;
        right: 20px;
    }
    
    /* Sections */
    .section-title {
        font-size: 2.2rem;
    }
    
    .about-section,
    .services-section,
    .projects-section {
        padding: 60px 0;
    }
    
    /* Contact */
    .contact-form-wrapper,
    .contact-info-wrapper {
        padding: 30px;
        margin-bottom: 30px;
    }
    
    /* Footer */
    .footer-main {
        padding: 50px 0 30px;
    }
    
    .footer-widget {
        margin-bottom: 30px;
    }
}

/* Mobile Large (576px to 767px) */
@media (max-width: 767.98px) {
    /* Typography */
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.2rem;
    }
    
    .hero-description {
        font-size: 1rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .page-title {
        font-size: 2.5rem;
    }
    
    /* Top Bar */
    .top-bar {
        text-align: center;
    }
    
    .top-bar .row > div {
        margin-bottom: 10px;
    }
    
    .top-bar .row > div:last-child {
        margin-bottom: 0;
    }
    
    .contact-info a {
        display: block;
        margin: 5px 0;
    }
    
    /* Header */
    .navbar-brand .logo {
        max-height: 50px;
    }
    
    /* Hero Section */
    .hero-section {
        height: 70vh;
        min-height: 500px;
    }
    
    .hero-content {
        padding: 0 15px;
    }
    
    .hero-nav {
        display: none;
    }
    
    /* Services */
    .service-card {
        margin-bottom: 30px;
    }
    
    .service-image {
        height: 200px;
    }
    
    .service-content {
        padding: 25px 20px;
    }
    
    /* Projects */
    .project-card {
        height: 250px;
        margin-bottom: 20px;
    }
    
    .project-overlay {
        padding: 20px;
    }
    
    /* About */
    .about-services {
        justify-content: center;
    }
    
    .service-tag {
        font-size: 11px;
        padding: 6px 12px;
    }
    
    /* Contact */
    .contact-form-wrapper,
    .contact-info-wrapper {
        padding: 25px 20px;
    }
    
    .contact-info-item {
        margin-bottom: 25px;
    }
    
    .contact-icon {
        width: 50px;
        height: 50px;
        margin-right: 15px;
    }
    
    /* CTA Section */
    .cta-title {
        font-size: 1.5rem;
    }
    
    .cta-description {
        font-size: 1rem;
    }
    
    /* Footer */
    .footer-main {
        padding: 40px 0 20px;
    }
    
    .footer-bottom {
        text-align: center;
    }
    
    .footer-bottom-links {
        margin-top: 10px;
    }
    
    .footer-bottom-links a {
        margin: 0 10px;
    }
    
    /* Back to Top */
    .back-to-top {
        bottom: 20px;
        right: 20px;
        width: 45px;
        height: 45px;
    }
}

/* Mobile Small (up to 575px) */
@media (max-width: 575.98px) {
    /* Typography */
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
    }
    
    .section-title {
        font-size: 1.8rem;
    }
    
    .page-title {
        font-size: 2rem;
    }
    
    /* Spacing */
    .about-section,
    .services-section,
    .projects-section,
    .contact-section {
        padding: 50px 0;
    }
    
    .page-header {
        padding: 60px 0 40px;
    }
    
    /* Hero Section */
    .hero-section {
        height: 60vh;
        min-height: 450px;
    }
    
    .hero-buttons .btn {
        width: 100%;
        margin-bottom: 10px;
    }
    
    /* Services */
    .service-image {
        height: 180px;
    }
    
    .service-content {
        padding: 20px 15px;
    }
    
    /* Projects */
    .project-card {
        height: 220px;
    }
    
    /* Contact */
    .contact-form-wrapper,
    .contact-info-wrapper {
        padding: 20px 15px;
    }
    
    .contact-info-item {
        flex-direction: column;
        text-align: center;
        margin-bottom: 30px;
    }
    
    .contact-icon {
        margin-right: 0;
        margin-bottom: 15px;
        align-self: center;
    }
    
    /* Form */
    .form-control {
        padding: 10px 12px;
        font-size: 14px;
    }
    
    .btn {
        padding: 10px 25px;
        font-size: 14px;
    }
    
    /* Footer */
    .footer-widget {
        text-align: center;
        margin-bottom: 40px;
    }
    
    .footer-social,
    .contact-social .social-links {
        justify-content: center;
    }
    
    .footer-contact .contact-item {
        justify-content: center;
    }
}

/* Extra Small Mobile (up to 320px) */
@media (max-width: 320px) {
    .hero-title {
        font-size: 1.8rem;
    }
    
    .section-title {
        font-size: 1.6rem;
    }
    
    .hero-section {
        height: 50vh;
        min-height: 400px;
    }
    
    .contact-form-wrapper,
    .contact-info-wrapper {
        padding: 15px 10px;
    }
    
    .service-content,
    .project-overlay {
        padding: 15px 10px;
    }
}

/* Landscape Mobile */
@media (max-height: 500px) and (orientation: landscape) {
    .hero-section {
        height: 100vh;
    }
    
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-subtitle {
        font-size: 1rem;
    }
    
    .hero-description {
        font-size: 0.9rem;
        margin-bottom: 1rem;
    }
    
    .hero-buttons .btn {
        padding: 8px 20px;
        font-size: 14px;
    }
}

/* Print Styles */
@media print {
    .top-bar,
    .navbar,
    .hero-nav,
    .back-to-top,
    .footer {
        display: none !important;
    }
    
    .hero-section {
        height: auto;
        padding: 50px 0;
    }
    
    .hero-overlay {
        background: rgba(0,0,0,0.1);
    }
    
    .hero-content {
        color: var(--dark-color);
    }
    
    body {
        font-size: 12pt;
        line-height: 1.4;
    }
    
    h1, h2, h3, h4, h5, h6 {
        page-break-after: avoid;
    }
    
    .section-title {
        font-size: 18pt;
    }
    
    .page-title {
        font-size: 24pt;
    }
    
    a {
        color: var(--dark-color);
        text-decoration: underline;
    }
    
    .btn {
        border: 1px solid var(--dark-color);
        background: transparent;
        color: var(--dark-color);
    }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .hero-section {
        background-attachment: scroll;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .hero-slide {
        transition: none;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --dark-color: #ffffff;
        --light-color: #2c3e50;
        --white-color: #1a1a1a;
        --border-color: #495057;
    }
    
    body {
        background-color: var(--white-color);
        color: var(--dark-color);
    }
    
    .header {
        background-color: var(--light-color);
    }
    
    .service-card,
    .contact-form-wrapper,
    .contact-info-wrapper {
        background-color: var(--light-color);
    }
    
    .form-control {
        background-color: var(--white-color);
        color: var(--dark-color);
        border-color: var(--border-color);
    }
}
