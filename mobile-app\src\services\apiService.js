/**
 * API Service
 * Handles all API communications with the backend
 */

import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';

const BASE_URL = 'http://localhost/api'; // Change this to your actual API URL

class ApiService {
  constructor() {
    this.api = axios.create({
      baseURL: BASE_URL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor
    this.api.interceptors.request.use(
      (config) => {
        console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.api.interceptors.response.use(
      (response) => {
        console.log(`API Response: ${response.status} ${response.config.url}`);
        return response;
      },
      (error) => {
        console.error('API Response Error:', error.response?.status, error.response?.data);
        
        // Handle 401 errors (unauthorized)
        if (error.response?.status === 401) {
          this.handleUnauthorized();
        }
        
        return Promise.reject(error);
      }
    );
  }

  setAuthToken(token) {
    if (token) {
      this.api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    } else {
      delete this.api.defaults.headers.common['Authorization'];
    }
  }

  async handleUnauthorized() {
    // Clear stored auth data
    await AsyncStorage.removeItem('authToken');
    await AsyncStorage.removeItem('userData');
    
    // Redirect to login (this would be handled by the auth context)
    console.log('Unauthorized - redirecting to login');
  }

  // Authentication
  async login(username, password) {
    try {
      const response = await this.api.post('/auth', {
        username,
        password,
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  // Projects
  async getProjects(params = {}) {
    try {
      const response = await this.api.get('/projects', { params });
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  async getProject(id) {
    try {
      const response = await this.api.get(`/projects/${id}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  async createProject(projectData) {
    try {
      const response = await this.api.post('/projects', projectData);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  async updateProject(id, projectData) {
    try {
      const response = await this.api.put(`/projects/${id}`, projectData);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  async deleteProject(id) {
    try {
      const response = await this.api.delete(`/projects/${id}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  // Services
  async getServices() {
    try {
      const response = await this.api.get('/services');
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  async createService(serviceData) {
    try {
      const response = await this.api.post('/services', serviceData);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  async updateService(id, serviceData) {
    try {
      const response = await this.api.put(`/services/${id}`, serviceData);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  async deleteService(id) {
    try {
      const response = await this.api.delete(`/services/${id}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  // Contacts
  async getContacts(params = {}) {
    try {
      const response = await this.api.get('/contacts', { params });
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  async updateContactStatus(id, status) {
    try {
      const response = await this.api.put(`/contacts/${id}`, { status });
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  // Media
  async uploadMedia(formData) {
    try {
      const response = await this.api.post('/media', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  async getMedia(params = {}) {
    try {
      const response = await this.api.get('/media', { params });
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  async deleteMedia(id) {
    try {
      const response = await this.api.delete(`/media/${id}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  // Analytics
  async getAnalytics(days = 30) {
    try {
      const response = await this.api.get('/analytics', {
        params: { days },
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  // Settings
  async getSettings() {
    try {
      const response = await this.api.get('/settings');
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  async updateSettings(settings) {
    try {
      const response = await this.api.put('/settings', settings);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  // Utility methods
  async checkConnection() {
    try {
      const response = await this.api.get('/health');
      return response.status === 200;
    } catch (error) {
      return false;
    }
  }

  // File upload helper
  createFormData(file, additionalData = {}) {
    const formData = new FormData();
    
    formData.append('file', {
      uri: file.uri,
      type: file.type,
      name: file.fileName || 'upload.jpg',
    });

    Object.keys(additionalData).forEach(key => {
      formData.append(key, additionalData[key]);
    });

    return formData;
  }
}

export const apiService = new ApiService();
export default apiService;
