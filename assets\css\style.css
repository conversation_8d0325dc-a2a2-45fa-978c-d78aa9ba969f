/* Construction Company Website Styles */

/* CSS Variables */
:root {
    --primary-color: #ff6b35;
    --secondary-color: #2c3e50;
    --accent-color: #f39c12;
    --dark-color: #1a1a1a;
    --light-color: #f8f9fa;
    --white-color: #ffffff;
    --gray-color: #6c757d;
    --border-color: #dee2e6;
    
    --font-primary: '<PERSON><PERSON>', sans-serif;
    --font-heading: '<PERSON>', sans-serif;
    
    --transition: all 0.3s ease;
    --border-radius: 8px;
    --box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-primary);
    font-size: 16px;
    line-height: 1.6;
    color: var(--dark-color);
    overflow-x: hidden;
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-heading);
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 1rem;
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--secondary-color);
}

img {
    max-width: 100%;
    height: auto;
}

/* Buttons */
.btn {
    font-family: var(--font-heading);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
    padding: 12px 30px;
    border-radius: var(--border-radius);
    transition: var(--transition);
    border: 2px solid transparent;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--white-color);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    color: var(--white-color);
}

.btn-outline-light {
    border-color: var(--white-color);
    color: var(--white-color);
}

.btn-outline-light:hover {
    background-color: var(--white-color);
    color: var(--dark-color);
}

/* Top Bar */
.top-bar {
    background-color: var(--secondary-color);
    color: var(--white-color);
    padding: 10px 0;
    font-size: 14px;
}

.top-bar a {
    color: var(--white-color);
    margin-right: 20px;
}

.top-bar a:hover {
    color: var(--primary-color);
}

.top-bar i {
    margin-right: 5px;
}

.social-links a {
    display: inline-block;
    width: 35px;
    height: 35px;
    line-height: 35px;
    text-align: center;
    border-radius: 50%;
    background-color: rgba(255,255,255,0.1);
    margin-left: 10px;
    transition: var(--transition);
}

.social-links a:hover {
    background-color: var(--primary-color);
    color: var(--white-color);
}

/* Header */
.header {
    background-color: var(--white-color);
    box-shadow: var(--box-shadow);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.navbar {
    padding: 15px 0;
}

.navbar-brand .logo {
    max-height: 60px;
}

.navbar-nav .nav-link {
    font-family: var(--font-heading);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
    color: var(--dark-color);
    margin: 0 15px;
    padding: 10px 0;
    position: relative;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    color: var(--primary-color);
}

.navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--primary-color);
    transition: var(--transition);
}

.navbar-nav .nav-link:hover::after,
.navbar-nav .nav-link.active::after {
    width: 100%;
}

/* Hero Section */
.hero-section {
    position: relative;
    height: 100vh;
    overflow: hidden;
}

.hero-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    opacity: 0;
    transition: opacity 1s ease-in-out;
    display: flex;
    align-items: center;
}

.hero-slide.active {
    opacity: 1;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(44,62,80,0.8), rgba(255,107,53,0.6));
}

.hero-content {
    position: relative;
    z-index: 2;
    color: var(--white-color);
    text-align: center;
}

.hero-title {
    font-size: 4rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.hero-subtitle {
    font-size: 1.5rem;
    font-style: italic;
    margin-bottom: 1rem;
    opacity: 0.9;
}

.hero-description {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.hero-nav {
    position: absolute;
    bottom: 30px;
    right: 30px;
    z-index: 3;
    display: flex;
    gap: 10px;
}

.hero-nav button {
    width: 50px;
    height: 50px;
    border: 2px solid var(--white-color);
    background: rgba(255,255,255,0.2);
    color: var(--white-color);
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
}

.hero-nav button:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

/* Section Styles */
.section-header {
    margin-bottom: 3rem;
}

.section-subtitle {
    color: var(--primary-color);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 2px;
    font-size: 14px;
    display: block;
    margin-bottom: 0.5rem;
}

.section-title {
    font-size: 2.5rem;
    color: var(--secondary-color);
    margin-bottom: 1rem;
}

.section-description {
    font-size: 1.1rem;
    color: var(--gray-color);
    max-width: 800px;
    margin: 0 auto;
}

/* About Section */
.about-section {
    padding: 80px 0;
}

.about-services {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin: 20px 0;
}

.service-tag {
    background-color: var(--light-color);
    color: var(--secondary-color);
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: var(--transition);
}

.service-tag:hover {
    background-color: var(--primary-color);
    color: var(--white-color);
}

/* Services Section */
.services-section {
    padding: 80px 0;
}

.service-card {
    background: var(--white-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    height: 100%;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.15);
}

.service-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.service-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.service-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    opacity: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.service-card:hover .service-overlay {
    opacity: 0.9;
}

.service-overlay i {
    font-size: 3rem;
    color: var(--white-color);
}

.service-content {
    padding: 30px 25px;
}

.service-title {
    color: var(--secondary-color);
    margin-bottom: 15px;
}

.service-description {
    color: var(--gray-color);
    margin-bottom: 20px;
}

.service-link {
    color: var(--primary-color);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 14px;
}

/* Projects Section */
.projects-section {
    padding: 80px 0;
}

.project-card {
    position: relative;
    border-radius: var(--border-radius);
    overflow: hidden;
    height: 300px;
    margin-bottom: 30px;
}

.project-image {
    width: 100%;
    height: 100%;
    position: relative;
}

.project-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to top, rgba(0,0,0,0.8), transparent);
    opacity: 0;
    transition: var(--transition);
    display: flex;
    align-items: flex-end;
    padding: 30px;
}

.project-card:hover .project-overlay {
    opacity: 1;
}

.project-info {
    color: var(--white-color);
    width: 100%;
}

.project-category {
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 1px;
    color: var(--primary-color);
    display: block;
    margin-bottom: 10px;
}

.project-title {
    margin-bottom: 15px;
}

.project-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: var(--primary-color);
    color: var(--white-color);
    border-radius: 50%;
    transition: var(--transition);
}

.project-link:hover {
    background-color: var(--white-color);
    color: var(--primary-color);
}

/* CTA Section */
.cta-section {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
}

.cta-title {
    font-size: 2rem;
    margin-bottom: 10px;
}

.cta-description {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 0;
}

/* Page Header */
.page-header {
    background: linear-gradient(45deg, var(--secondary-color), var(--primary-color));
    color: var(--white-color);
    padding: 80px 0 60px;
    text-align: center;
}

.page-title {
    font-size: 3rem;
    margin-bottom: 20px;
}

.breadcrumb {
    background: none;
    padding: 0;
    margin: 0;
    justify-content: center;
}

.breadcrumb-item a {
    color: rgba(255,255,255,0.8);
}

.breadcrumb-item.active {
    color: var(--white-color);
}

/* Contact Section */
.contact-section {
    padding: 80px 0;
}

.contact-form-wrapper,
.contact-info-wrapper {
    background: var(--white-color);
    padding: 40px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    height: 100%;
}

.contact-info-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 30px;
}

.contact-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-color);
    color: var(--white-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    flex-shrink: 0;
}

.contact-details h5 {
    color: var(--secondary-color);
    margin-bottom: 5px;
}

.contact-details p {
    color: var(--gray-color);
    margin: 0;
}

.contact-social .social-links {
    display: flex;
    gap: 10px;
}

.contact-social .social-links a {
    width: 40px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    background: var(--light-color);
    color: var(--secondary-color);
    border-radius: 50%;
    transition: var(--transition);
}

.contact-social .social-links a:hover {
    background: var(--primary-color);
    color: var(--white-color);
}

/* Form Styles */
.form-control {
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 12px 15px;
    font-size: 16px;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(255,107,53,0.25);
}

.form-label {
    font-weight: 600;
    color: var(--secondary-color);
    margin-bottom: 8px;
}

/* Map Section */
.map-wrapper {
    position: relative;
}

.map-wrapper iframe {
    filter: grayscale(100%);
    transition: var(--transition);
}

.map-wrapper:hover iframe {
    filter: grayscale(0%);
}

/* Footer */
.footer {
    background-color: var(--secondary-color);
    color: var(--white-color);
}

.footer-main {
    padding: 60px 0 40px;
}

.footer-widget h5 {
    color: var(--white-color);
    margin-bottom: 20px;
}

.footer-description {
    color: rgba(255,255,255,0.8);
    margin-bottom: 20px;
}

.footer-links {
    list-style: none;
    padding: 0;
}

.footer-links li {
    margin-bottom: 10px;
}

.footer-links a {
    color: rgba(255,255,255,0.8);
    transition: var(--transition);
}

.footer-links a:hover {
    color: var(--primary-color);
}

.footer-social {
    display: flex;
    gap: 10px;
}

.footer-social a {
    width: 40px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    background: rgba(255,255,255,0.1);
    color: var(--white-color);
    border-radius: 50%;
    transition: var(--transition);
}

.footer-social a:hover {
    background: var(--primary-color);
}

.footer-contact .contact-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.footer-contact .contact-item i {
    width: 20px;
    margin-right: 10px;
    color: var(--primary-color);
}

.footer-contact a {
    color: rgba(255,255,255,0.8);
}

.footer-contact a:hover {
    color: var(--white-color);
}

.footer-bottom {
    border-top: 1px solid rgba(255,255,255,0.1);
    padding: 20px 0;
}

.copyright {
    color: rgba(255,255,255,0.8);
    margin: 0;
}

.footer-bottom-links a {
    color: rgba(255,255,255,0.8);
    margin-left: 20px;
}

.footer-bottom-links a:hover {
    color: var(--white-color);
}

/* Back to Top */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    color: var(--white-color);
    border: none;
    border-radius: 50%;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    z-index: 1000;
}

.back-to-top.show {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    background: var(--secondary-color);
}

/* Utility Classes */
.bg-primary {
    background-color: var(--primary-color) !important;
}

.bg-secondary {
    background-color: var(--secondary-color) !important;
}

.text-primary {
    color: var(--primary-color) !important;
}

.text-secondary {
    color: var(--secondary-color) !important;
}
