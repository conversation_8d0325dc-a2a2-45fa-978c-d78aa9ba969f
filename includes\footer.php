    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-main">
            <div class="container">
                <div class="row">
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="footer-widget">
                            <div class="footer-logo">
                                <img src="/assets/images/logo-white.png" alt="<?php echo get_setting('company_name', 'Construction Company'); ?>">
                            </div>
                            <p class="footer-description">
                                We are committed to delivering high-quality construction services with professionalism and dedication to customer satisfaction.
                            </p>
                            <div class="footer-social">
                                <?php if (get_setting('facebook_url')): ?>
                                <a href="<?php echo get_setting('facebook_url'); ?>" target="_blank"><i class="fab fa-facebook-f"></i></a>
                                <?php endif; ?>
                                <?php if (get_setting('instagram_url')): ?>
                                <a href="<?php echo get_setting('instagram_url'); ?>" target="_blank"><i class="fab fa-instagram"></i></a>
                                <?php endif; ?>
                                <?php if (get_setting('youtube_url')): ?>
                                <a href="<?php echo get_setting('youtube_url'); ?>" target="_blank"><i class="fab fa-youtube"></i></a>
                                <?php endif; ?>
                                <?php if (get_setting('linkedin_url')): ?>
                                <a href="<?php echo get_setting('linkedin_url'); ?>" target="_blank"><i class="fab fa-linkedin-in"></i></a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-2 col-md-6 mb-4">
                        <div class="footer-widget">
                            <h5 class="footer-title">Company</h5>
                            <ul class="footer-links">
                                <li><a href="/">Home</a></li>
                                <li><a href="/?page=about">About Us</a></li>
                                <li><a href="/?page=services">Our Services</a></li>
                                <li><a href="/?page=projects">Our Projects</a></li>
                                <li><a href="/?page=media">Media</a></li>
                                <li><a href="/?page=contact">Contact Us</a></li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="footer-widget">
                            <h5 class="footer-title">Services</h5>
                            <ul class="footer-links">
                                <?php
                                $stmt = $db->prepare("SELECT title, slug FROM services WHERE is_active = 1 ORDER BY sort_order LIMIT 5");
                                $stmt->execute();
                                $services = $stmt->fetchAll();
                                foreach ($services as $service):
                                ?>
                                <li><a href="/?page=service&slug=<?php echo $service['slug']; ?>"><?php echo $service['title']; ?></a></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="footer-widget">
                            <h5 class="footer-title">Contact Info</h5>
                            <div class="footer-contact">
                                <div class="contact-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span><?php echo get_setting('company_address', ''); ?></span>
                                </div>
                                <div class="contact-item">
                                    <i class="fas fa-phone"></i>
                                    <span>
                                        <a href="tel:<?php echo get_setting('company_phone', ''); ?>"><?php echo get_setting('company_phone', ''); ?></a>
                                    </span>
                                </div>
                                <?php if (get_setting('company_mobile')): ?>
                                <div class="contact-item">
                                    <i class="fas fa-mobile-alt"></i>
                                    <span>
                                        <a href="tel:<?php echo get_setting('company_mobile', ''); ?>"><?php echo get_setting('company_mobile', ''); ?></a>
                                    </span>
                                </div>
                                <?php endif; ?>
                                <div class="contact-item">
                                    <i class="fas fa-envelope"></i>
                                    <span>
                                        <a href="mailto:<?php echo get_setting('company_email', ''); ?>"><?php echo get_setting('company_email', ''); ?></a>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer-bottom">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <p class="copyright">
                            &copy; <?php echo date('Y'); ?> <?php echo get_setting('company_name', 'Construction Company'); ?>. All rights reserved.
                        </p>
                    </div>
                    <div class="col-md-6">
                        <div class="footer-bottom-links text-end">
                            <a href="/?page=privacy">Privacy Policy</a>
                            <a href="/?page=terms">Terms of Service</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <button class="back-to-top" id="backToTop">
        <i class="fas fa-chevron-up"></i>
    </button>

    <!-- JavaScript -->
    <script src="/assets/js/bootstrap.bundle.min.js"></script>
    <script src="/assets/js/jquery.min.js"></script>
    <script src="/assets/js/main.js"></script>
    
    <!-- Custom page scripts -->
    <?php if (isset($page_scripts)): ?>
        <?php echo $page_scripts; ?>
    <?php endif; ?>
</body>
</html>
