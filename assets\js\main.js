/**
 * Construction Company Website JavaScript
 * Main functionality and interactions
 */

(function($) {
    'use strict';

    // Document Ready
    $(document).ready(function() {
        initializeComponents();
        bindEvents();
        initializeAnimations();
    });

    // Initialize all components
    function initializeComponents() {
        initBackToTop();
        initSmoothScroll();
        initLazyLoading();
        initTooltips();
        initFormValidation();
    }

    // Bind all events
    function bindEvents() {
        bindNavigationEvents();
        bindScrollEvents();
        bindFormEvents();
        bindModalEvents();
    }

    // Back to Top Button
    function initBackToTop() {
        const backToTopBtn = $('#backToTop');
        
        if (backToTopBtn.length) {
            backToTopBtn.on('click', function(e) {
                e.preventDefault();
                $('html, body').animate({
                    scrollTop: 0
                }, 800, 'easeInOutQuart');
            });
        }
    }

    // Smooth Scrolling for Anchor Links
    function initSmoothScroll() {
        $('a[href^="#"]').on('click', function(e) {
            const target = $(this.getAttribute('href'));
            
            if (target.length) {
                e.preventDefault();
                $('html, body').animate({
                    scrollTop: target.offset().top - 100
                }, 800, 'easeInOutQuart');
            }
        });
    }

    // Lazy Loading for Images
    function initLazyLoading() {
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });

            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        }
    }

    // Initialize Tooltips
    function initTooltips() {
        if (typeof bootstrap !== 'undefined') {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function(tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }
    }

    // Form Validation
    function initFormValidation() {
        $('.contact-form').on('submit', function(e) {
            const form = $(this);
            let isValid = true;

            // Clear previous errors
            form.find('.is-invalid').removeClass('is-invalid');
            form.find('.invalid-feedback').remove();

            // Validate required fields
            form.find('[required]').each(function() {
                const field = $(this);
                const value = field.val().trim();

                if (!value) {
                    showFieldError(field, 'This field is required');
                    isValid = false;
                }
            });

            // Validate email
            const emailField = form.find('input[type="email"]');
            if (emailField.length && emailField.val()) {
                if (!isValidEmail(emailField.val())) {
                    showFieldError(emailField, 'Please enter a valid email address');
                    isValid = false;
                }
            }

            // Validate phone
            const phoneField = form.find('input[type="tel"]');
            if (phoneField.length && phoneField.val()) {
                if (!isValidPhone(phoneField.val())) {
                    showFieldError(phoneField, 'Please enter a valid phone number');
                    isValid = false;
                }
            }

            if (!isValid) {
                e.preventDefault();
                // Scroll to first error
                const firstError = form.find('.is-invalid').first();
                if (firstError.length) {
                    $('html, body').animate({
                        scrollTop: firstError.offset().top - 100
                    }, 500);
                }
            }
        });
    }

    // Show field error
    function showFieldError(field, message) {
        field.addClass('is-invalid');
        field.after('<div class="invalid-feedback">' + message + '</div>');
    }

    // Email validation
    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    // Phone validation
    function isValidPhone(phone) {
        const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
        return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
    }

    // Navigation Events
    function bindNavigationEvents() {
        // Mobile menu toggle
        $('.navbar-toggler').on('click', function() {
            $(this).toggleClass('active');
        });

        // Close mobile menu when clicking outside
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.navbar').length) {
                $('.navbar-collapse').collapse('hide');
                $('.navbar-toggler').removeClass('active');
            }
        });

        // Active navigation highlighting
        updateActiveNavigation();
    }

    // Update active navigation based on current page
    function updateActiveNavigation() {
        const currentPage = getUrlParameter('page') || 'home';
        $('.navbar-nav .nav-link').removeClass('active');
        $(`.navbar-nav .nav-link[href*="${currentPage}"]`).addClass('active');
    }

    // Scroll Events
    function bindScrollEvents() {
        let ticking = false;

        $(window).on('scroll', function() {
            if (!ticking) {
                requestAnimationFrame(function() {
                    handleScroll();
                    ticking = false;
                });
                ticking = true;
            }
        });
    }

    // Handle scroll events
    function handleScroll() {
        const scrollTop = $(window).scrollTop();
        
        // Back to top button visibility
        const backToTopBtn = $('#backToTop');
        if (scrollTop > 300) {
            backToTopBtn.addClass('show');
        } else {
            backToTopBtn.removeClass('show');
        }

        // Header background on scroll
        const header = $('.header');
        if (scrollTop > 100) {
            header.addClass('scrolled');
        } else {
            header.removeClass('scrolled');
        }

        // Parallax effect for hero section
        if ($('.hero-section').length) {
            const parallaxSpeed = 0.5;
            $('.hero-section').css('transform', `translateY(${scrollTop * parallaxSpeed}px)`);
        }
    }

    // Form Events
    function bindFormEvents() {
        // File upload preview
        $('input[type="file"]').on('change', function() {
            const file = this.files[0];
            const preview = $(this).siblings('.file-preview');
            
            if (file && preview.length) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.html(`<img src="${e.target.result}" alt="Preview" class="img-thumbnail" style="max-width: 200px;">`);
                };
                reader.readAsDataURL(file);
            }
        });

        // Auto-resize textareas
        $('textarea').on('input', function() {
            this.style.height = 'auto';
            this.style.height = (this.scrollHeight) + 'px';
        });
    }

    // Modal Events
    function bindModalEvents() {
        // Close modal on outside click
        $('.modal').on('click', function(e) {
            if (e.target === this) {
                $(this).modal('hide');
            }
        });
    }

    // Initialize Animations
    function initializeAnimations() {
        // Fade in animation for elements
        if ('IntersectionObserver' in window) {
            const animationObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate-in');
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            });

            document.querySelectorAll('.animate-on-scroll').forEach(el => {
                animationObserver.observe(el);
            });
        }

        // Counter animation
        $('.counter').each(function() {
            const $this = $(this);
            const countTo = $this.attr('data-count');
            
            $({ countNum: $this.text() }).animate({
                countNum: countTo
            }, {
                duration: 2000,
                easing: 'linear',
                step: function() {
                    $this.text(Math.floor(this.countNum));
                },
                complete: function() {
                    $this.text(this.countNum);
                }
            });
        });
    }

    // Utility Functions
    function getUrlParameter(name) {
        name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
        const regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
        const results = regex.exec(location.search);
        return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
    }

    // Debounce function
    function debounce(func, wait, immediate) {
        let timeout;
        return function() {
            const context = this;
            const args = arguments;
            const later = function() {
                timeout = null;
                if (!immediate) func.apply(context, args);
            };
            const callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func.apply(context, args);
        };
    }

    // Throttle function
    function throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    // Loading overlay
    function showLoading() {
        if (!$('.loading-overlay').length) {
            $('body').append('<div class="loading-overlay"><div class="spinner"></div></div>');
        }
        $('.loading-overlay').fadeIn();
    }

    function hideLoading() {
        $('.loading-overlay').fadeOut();
    }

    // Notification system
    function showNotification(message, type = 'info', duration = 5000) {
        const notification = $(`
            <div class="notification notification-${type}">
                <span class="notification-message">${message}</span>
                <button class="notification-close">&times;</button>
            </div>
        `);

        $('body').append(notification);
        notification.fadeIn();

        // Auto hide
        setTimeout(() => {
            notification.fadeOut(() => notification.remove());
        }, duration);

        // Manual close
        notification.find('.notification-close').on('click', () => {
            notification.fadeOut(() => notification.remove());
        });
    }

    // AJAX form submission
    function submitFormAjax(form, successCallback, errorCallback) {
        const formData = new FormData(form[0]);
        
        showLoading();
        
        $.ajax({
            url: form.attr('action') || window.location.href,
            type: form.attr('method') || 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                hideLoading();
                if (successCallback) successCallback(response);
            },
            error: function(xhr, status, error) {
                hideLoading();
                if (errorCallback) errorCallback(xhr, status, error);
            }
        });
    }

    // Image gallery lightbox
    function initImageGallery() {
        $('.gallery-item').on('click', function(e) {
            e.preventDefault();
            const src = $(this).attr('href');
            const alt = $(this).find('img').attr('alt') || '';
            
            const lightbox = $(`
                <div class="lightbox">
                    <div class="lightbox-content">
                        <img src="${src}" alt="${alt}">
                        <button class="lightbox-close">&times;</button>
                    </div>
                </div>
            `);
            
            $('body').append(lightbox);
            lightbox.fadeIn();
            
            lightbox.on('click', function(e) {
                if (e.target === this || $(e.target).hasClass('lightbox-close')) {
                    lightbox.fadeOut(() => lightbox.remove());
                }
            });
        });
    }

    // Expose public methods
    window.ConstructionSite = {
        showLoading: showLoading,
        hideLoading: hideLoading,
        showNotification: showNotification,
        submitFormAjax: submitFormAjax,
        initImageGallery: initImageGallery
    };

})(jQuery);

// Custom easing functions
jQuery.easing.easeInOutQuart = function(x, t, b, c, d) {
    if ((t /= d / 2) < 1) return c / 2 * t * t * t * t + b;
    return -c / 2 * ((t -= 2) * t * t * t - 2) + b;
};

// Service Worker Registration (for PWA capabilities)
if ('serviceWorker' in navigator) {
    window.addEventListener('load', function() {
        navigator.serviceWorker.register('/sw.js')
            .then(function(registration) {
                console.log('ServiceWorker registration successful');
            })
            .catch(function(err) {
                console.log('ServiceWorker registration failed');
            });
    });
}
