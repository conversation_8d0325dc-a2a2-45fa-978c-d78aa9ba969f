<?php
// Main entry point for the construction company website
session_start();

// Configuration
define('BASE_PATH', __DIR__);
define('BASE_URL', 'http://localhost');

// Database configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'construction_company');
define('DB_USER', 'root');
define('DB_PASS', '');

// Include core files
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/router.php';

// Initialize database connection
$database = new Database();
$db = $database->getConnection();

// Get the current page from URL
$page = isset($_GET['page']) ? $_GET['page'] : 'home';

// Route the request
$router = new Router();
$router->route($page);
?>
