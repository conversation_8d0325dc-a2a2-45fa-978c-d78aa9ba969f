<?php
$page_title = "Contact Us";
$page_description = "Get in touch with our construction experts. Contact us for quotes, consultations, and project inquiries.";

$success_message = '';
$error_message = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $name = sanitize_input($_POST['name'] ?? '');
        $email = sanitize_input($_POST['email'] ?? '');
        $phone = sanitize_input($_POST['phone'] ?? '');
        $subject = sanitize_input($_POST['subject'] ?? '');
        $message = sanitize_input($_POST['message'] ?? '');
        
        // Validation
        $errors = [];
        if (empty($name)) $errors[] = "Name is required";
        if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) $errors[] = "Valid email is required";
        if (empty($message)) $errors[] = "Message is required";
        
        if (empty($errors)) {
            try {
                $stmt = $db->prepare("INSERT INTO contact_submissions (name, email, phone, subject, message, ip_address, user_agent) VALUES (?, ?, ?, ?, ?, ?, ?)");
                $stmt->execute([
                    $name, 
                    $email, 
                    $phone, 
                    $subject, 
                    $message, 
                    $_SERVER['REMOTE_ADDR'] ?? '', 
                    $_SERVER['HTTP_USER_AGENT'] ?? ''
                ]);
                
                $success_message = "Thank you for your message! We'll get back to you soon.";
                
                // Clear form data
                $name = $email = $phone = $subject = $message = '';
                
            } catch (Exception $e) {
                $error_message = "Sorry, there was an error sending your message. Please try again.";
            }
        } else {
            $error_message = implode('<br>', $errors);
        }
    } else {
        $error_message = "Security token mismatch. Please try again.";
    }
}
?>

<!-- Page Header -->
<section class="page-header">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h1 class="page-title">Contact Us</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="/">Home</a></li>
                        <li class="breadcrumb-item active">Contact Us</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</section>

<!-- Contact Section -->
<section class="contact-section py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mb-5">
                <div class="contact-form-wrapper">
                    <h3 class="mb-4">Get In Touch</h3>
                    
                    <?php if ($success_message): ?>
                    <div class="alert alert-success"><?php echo $success_message; ?></div>
                    <?php endif; ?>
                    
                    <?php if ($error_message): ?>
                    <div class="alert alert-danger"><?php echo $error_message; ?></div>
                    <?php endif; ?>
                    
                    <form method="POST" class="contact-form">
                        <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">Full Name *</label>
                                <input type="text" class="form-control" id="name" name="name" value="<?php echo htmlspecialchars($name ?? ''); ?>" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email Address *</label>
                                <input type="email" class="form-control" id="email" name="email" value="<?php echo htmlspecialchars($email ?? ''); ?>" required>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">Phone Number</label>
                                <input type="tel" class="form-control" id="phone" name="phone" value="<?php echo htmlspecialchars($phone ?? ''); ?>">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="subject" class="form-label">Subject</label>
                                <select class="form-control" id="subject" name="subject">
                                    <option value="">Select a subject</option>
                                    <option value="General Inquiry" <?php echo ($subject ?? '') == 'General Inquiry' ? 'selected' : ''; ?>>General Inquiry</option>
                                    <option value="Project Quote" <?php echo ($subject ?? '') == 'Project Quote' ? 'selected' : ''; ?>>Project Quote</option>
                                    <option value="Civil Engineering" <?php echo ($subject ?? '') == 'Civil Engineering' ? 'selected' : ''; ?>>Civil Engineering</option>
                                    <option value="Groundworks" <?php echo ($subject ?? '') == 'Groundworks' ? 'selected' : ''; ?>>Groundworks</option>
                                    <option value="RC Frames" <?php echo ($subject ?? '') == 'RC Frames' ? 'selected' : ''; ?>>RC Frames</option>
                                    <option value="Basements" <?php echo ($subject ?? '') == 'Basements' ? 'selected' : ''; ?>>Basements</option>
                                    <option value="Hard Landscaping" <?php echo ($subject ?? '') == 'Hard Landscaping' ? 'selected' : ''; ?>>Hard Landscaping</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="message" class="form-label">Message *</label>
                            <textarea class="form-control" id="message" name="message" rows="6" required><?php echo htmlspecialchars($message ?? ''); ?></textarea>
                        </div>
                        
                        <button type="submit" class="btn btn-primary btn-lg">Send Message</button>
                    </form>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="contact-info-wrapper">
                    <h3 class="mb-4">Contact Information</h3>
                    
                    <div class="contact-info-item">
                        <div class="contact-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div class="contact-details">
                            <h5>Address</h5>
                            <p><?php echo get_setting('company_address', ''); ?></p>
                        </div>
                    </div>
                    
                    <div class="contact-info-item">
                        <div class="contact-icon">
                            <i class="fas fa-phone"></i>
                        </div>
                        <div class="contact-details">
                            <h5>Phone</h5>
                            <p><a href="tel:<?php echo get_setting('company_phone', ''); ?>"><?php echo get_setting('company_phone', ''); ?></a></p>
                        </div>
                    </div>
                    
                    <?php if (get_setting('company_mobile')): ?>
                    <div class="contact-info-item">
                        <div class="contact-icon">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <div class="contact-details">
                            <h5>Mobile</h5>
                            <p><a href="tel:<?php echo get_setting('company_mobile', ''); ?>"><?php echo get_setting('company_mobile', ''); ?></a></p>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <div class="contact-info-item">
                        <div class="contact-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="contact-details">
                            <h5>Email</h5>
                            <p><a href="mailto:<?php echo get_setting('company_email', ''); ?>"><?php echo get_setting('company_email', ''); ?></a></p>
                        </div>
                    </div>
                    
                    <div class="contact-social mt-4">
                        <h5>Follow Us</h5>
                        <div class="social-links">
                            <?php if (get_setting('facebook_url')): ?>
                            <a href="<?php echo get_setting('facebook_url'); ?>" target="_blank"><i class="fab fa-facebook-f"></i></a>
                            <?php endif; ?>
                            <?php if (get_setting('instagram_url')): ?>
                            <a href="<?php echo get_setting('instagram_url'); ?>" target="_blank"><i class="fab fa-instagram"></i></a>
                            <?php endif; ?>
                            <?php if (get_setting('youtube_url')): ?>
                            <a href="<?php echo get_setting('youtube_url'); ?>" target="_blank"><i class="fab fa-youtube"></i></a>
                            <?php endif; ?>
                            <?php if (get_setting('linkedin_url')): ?>
                            <a href="<?php echo get_setting('linkedin_url'); ?>" target="_blank"><i class="fab fa-linkedin-in"></i></a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Map Section -->
<section class="map-section">
    <div class="container-fluid p-0">
        <div class="map-wrapper">
            <iframe 
                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2479.123456789!2d-0.123456!3d51.123456!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zNTHCsDA3JzI0LjQiTiAwwrAwNycyNC40Ilc!5e0!3m2!1sen!2suk!4v1234567890123!5m2!1sen!2suk" 
                width="100%" 
                height="400" 
                style="border:0;" 
                allowfullscreen="" 
                loading="lazy" 
                referrerpolicy="no-referrer-when-downgrade">
            </iframe>
        </div>
    </div>
</section>
