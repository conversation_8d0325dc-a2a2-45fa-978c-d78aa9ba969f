# Construction Company Web Platform

A comprehensive construction company web platform with responsive website, admin panel, and mobile application for content management.

## 🏗️ Features

### Website Features

- **Responsive Design**: Mobile-first approach with support for all devices (320px to 2560px)
- **Professional Aesthetic**: Clean design inspired by construction industry standards
- **SEO Optimized**: Structured data, meta tags, and performance optimization
- **Interactive Elements**: Project galleries, service showcases, contact forms
- **Performance Optimized**: Lazy loading, optimized images, fast loading times (<3s)

### Admin Panel Features

- **Dashboard**: Analytics, statistics, and overview
- **Project Management**: CRUD operations for projects with image galleries
- **Service Management**: Manage service catalog and descriptions
- **Media Library**: Upload and organize images and videos
- **Contact Management**: Handle contact form submissions
- **User Management**: Admin user accounts and permissions
- **Analytics**: Website traffic and visitor statistics

### Mobile Application Features

- **Cross-Platform**: React Native app for iOS and Android
- **Real-time Management**: Live content updates
- **Offline Capability**: Work without internet connection
- **Push Notifications**: Stay updated with new messages and activities
- **Media Upload**: Direct photo/video upload from mobile device
- **Secure Authentication**: JWT-based authentication system

## 🛠️ Technology Stack

### Frontend

- **HTML5/CSS3/JavaScript (ES6+)**
- **Bootstrap 5**: Responsive framework
- **Font Awesome**: Icon library
- **Custom CSS**: Professional styling with CSS variables

### Backend

- **PHP 8.0+**: Server-side logic
- **MySQL 8.0+**: Database management
- **RESTful API**: JSON-based API for mobile app
- **MVC Architecture**: Organized code structure

### Mobile App

- **React Native**: Cross-platform mobile development
- **React Navigation**: Navigation system
- **React Query**: Data fetching and caching
- **React Native Paper**: Material Design components
- **AsyncStorage**: Local data storage

## 📋 Requirements

### Server Requirements

- PHP 8.0 or higher
- MySQL 8.0 or higher
- Apache/Nginx web server
- mod_rewrite enabled
- GD extension for image processing
- PDO MySQL extension

### Development Requirements

- Node.js 16+ (for mobile app)
- React Native CLI
- Android Studio (for Android development)
- Xcode (for iOS development, macOS only)

## 🚀 Installation

### 1. Website & Admin Panel Setup

1. **Clone/Download the project**

   ```bash
   # If using Git
   git clone <repository-url>
   
   # Or download and extract to your web server directory
   # Example: c:\xampp\htdocs\construction-company
   ```

2. **Database Setup**

   ```bash
   # Import the database schema
   mysql -u root -p < database/schema.sql
   
   # Or use phpMyAdmin to import the schema.sql file
   ```

3. **Configuration**

   ```php
   // Update config/database.php with your database credentials
   define('DB_HOST', 'localhost');
   define('DB_NAME', 'construction_company');
   define('DB_USER', 'root');
   define('DB_PASS', '');
   ```

4. **File Permissions**

   ```bash
   # Set proper permissions for upload directories
   chmod 755 uploads/
   chmod 755 assets/images/
   ```

5. **Access the Website**
   - Website: `http://localhost/construction-company/`
   - Admin Panel: `http://localhost/construction-company/admin/`
   - Default Admin Login: `admin` / `admin123`

### 2. Mobile App Setup

1. **Install Dependencies**

   ```bash
   cd mobile-app
   npm install
   
   # For iOS (macOS only)
   cd ios && pod install && cd ..
   ```

2. **Configure API URL**

   ```javascript
   // Update src/services/apiService.js
   const BASE_URL = 'http://your-domain.com/api'; // Replace with your actual URL
   ```

3. **Run the App**

   ```bash
   # Start Metro bundler
   npm start
   
   # Run on Android
   npm run android
   
   # Run on iOS (macOS only)
   npm run ios
   ```

## 📱 Mobile App Development

### Android Setup

1. Install Android Studio
2. Set up Android SDK
3. Create virtual device or connect physical device
4. Enable USB debugging

### iOS Setup (macOS only)

1. Install Xcode
2. Install iOS Simulator
3. Set up Apple Developer account (for device testing)

### Building for Production

```bash
# Android APK
npm run build:android

# iOS Archive (macOS only)
npm run build:ios
```

## 🔧 Configuration

### Website Configuration

- Update company information in `database/schema.sql` settings
- Customize colors and styling in `assets/css/style.css`
- Add your logo to `assets/images/logo.png`
- Configure Google Maps in contact page

### API Configuration

- Update CORS settings in `api/index.php`
- Configure JWT secret for production
- Set up proper error logging

### Mobile App Configuration

- Update app name and bundle ID
- Configure push notifications
- Set up app icons and splash screens

## 📊 Database Schema

### Main Tables

- **users**: Admin user accounts
- **projects**: Project portfolio
- **services**: Service catalog
- **media**: Image and video library
- **contact_submissions**: Contact form data
- **analytics**: Website traffic data
- **settings**: Site configuration

## 🔐 Security Features

- **Input Validation**: All user inputs are sanitized
- **SQL Injection Prevention**: Prepared statements
- **XSS Protection**: Output escaping
- **CSRF Protection**: Token-based validation
- **File Upload Security**: Type and size validation
- **Authentication**: Secure login system
- **API Security**: JWT token authentication

## 📈 Performance Optimization

- **Image Optimization**: Lazy loading and compression
- **CSS/JS Minification**: Reduced file sizes
- **Database Indexing**: Optimized queries
- **Caching**: Browser and server-side caching
- **CDN Ready**: External asset support

## 🎨 Customization

### Styling

- Modify CSS variables in `assets/css/style.css`
- Update responsive breakpoints in `assets/css/responsive.css`
- Customize admin panel styling in `admin/assets/css/admin.css`

### Content

- Update company information in database settings
- Add/modify services in admin panel
- Upload company logo and images
- Customize page content in PHP templates

## 🐛 Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Check database credentials in `config/database.php`
   - Ensure MySQL service is running
   - Verify database exists

2. **File Upload Issues**
   - Check directory permissions (755)
   - Verify PHP upload settings
   - Ensure sufficient disk space

3. **Mobile App Build Errors**
   - Clear Metro cache: `npx react-native start --reset-cache`
   - Clean build: `cd android && ./gradlew clean`
   - Update dependencies: `npm update`

4. **API Connection Issues**
   - Verify API URL in mobile app
   - Check CORS settings
   - Ensure server is accessible

## 📞 Support

For technical support or questions:

- Check the troubleshooting section
- Review error logs in server error log
- Ensure all requirements are met
- Verify configuration settings

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📝 Changelog

### Version 1.0.0

- Initial release
- Responsive website
- Admin panel
- Mobile application
- RESTful API
- Complete documentation
