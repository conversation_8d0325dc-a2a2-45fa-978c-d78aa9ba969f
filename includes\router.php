<?php
class Router {
    private $routes = [
        'home' => 'pages/home.php',
        'about' => 'pages/about.php',
        'services' => 'pages/services.php',
        'projects' => 'pages/projects.php',
        'media' => 'pages/media.php',
        'contact' => 'pages/contact.php',
        'service' => 'pages/service-detail.php',
        'project' => 'pages/project-detail.php'
    ];

    public function route($page) {
        // Log analytics
        log_analytics($_SERVER['REQUEST_URI']);
        
        // Check if route exists
        if (array_key_exists($page, $this->routes)) {
            $this->loadPage($this->routes[$page]);
        } else {
            $this->loadPage('pages/404.php');
        }
    }

    private function loadPage($page_file) {
        if (file_exists($page_file)) {
            include 'includes/header.php';
            include $page_file;
            include 'includes/footer.php';
        } else {
            include 'includes/header.php';
            include 'pages/404.php';
            include 'includes/footer.php';
        }
    }
}
?>
