<?php
/**
 * Construction Company API
 * RESTful API for mobile application
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Include core files
require_once '../config/database.php';
require_once '../includes/functions.php';

// Initialize database connection
$database = new Database();
$db = $database->getConnection();

// Get request method and endpoint
$method = $_SERVER['REQUEST_METHOD'];
$request = $_SERVER['REQUEST_URI'];
$path = parse_url($request, PHP_URL_PATH);
$path = str_replace('/api', '', $path);
$segments = explode('/', trim($path, '/'));

// API Router
class APIRouter {
    private $db;
    private $method;
    private $segments;
    
    public function __construct($db, $method, $segments) {
        $this->db = $db;
        $this->method = $method;
        $this->segments = $segments;
    }
    
    public function route() {
        try {
            // Authentication check for protected endpoints
            if ($this->requiresAuth()) {
                $this->authenticate();
            }
            
            $endpoint = $this->segments[0] ?? '';
            
            switch ($endpoint) {
                case 'auth':
                    return $this->handleAuth();
                case 'projects':
                    return $this->handleProjects();
                case 'services':
                    return $this->handleServices();
                case 'media':
                    return $this->handleMedia();
                case 'contacts':
                    return $this->handleContacts();
                case 'analytics':
                    return $this->handleAnalytics();
                case 'settings':
                    return $this->handleSettings();
                default:
                    return $this->error('Endpoint not found', 404);
            }
        } catch (Exception $e) {
            return $this->error('Internal server error: ' . $e->getMessage(), 500);
        }
    }
    
    private function requiresAuth() {
        $publicEndpoints = ['auth'];
        $endpoint = $this->segments[0] ?? '';
        return !in_array($endpoint, $publicEndpoints);
    }
    
    private function authenticate() {
        $headers = getallheaders();
        $authHeader = $headers['Authorization'] ?? '';
        
        if (!$authHeader || !preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
            $this->error('Authorization header missing', 401);
        }
        
        $token = $matches[1];
        
        // Verify JWT token (simplified - in production use proper JWT library)
        $user = $this->verifyToken($token);
        if (!$user) {
            $this->error('Invalid or expired token', 401);
        }
        
        // Store user info for use in endpoints
        $GLOBALS['current_user'] = $user;
    }
    
    private function verifyToken($token) {
        // Simplified token verification - in production use proper JWT
        $stmt = $this->db->prepare("SELECT * FROM users WHERE id = ? AND is_active = 1");
        $stmt->execute([base64_decode($token)]);
        return $stmt->fetch();
    }
    
    private function handleAuth() {
        if ($this->method === 'POST') {
            return $this->login();
        }
        return $this->error('Method not allowed', 405);
    }
    
    private function login() {
        $input = json_decode(file_get_contents('php://input'), true);
        $username = $input['username'] ?? '';
        $password = $input['password'] ?? '';
        
        if (empty($username) || empty($password)) {
            return $this->error('Username and password required', 400);
        }
        
        $stmt = $this->db->prepare("SELECT * FROM users WHERE username = ? AND is_active = 1");
        $stmt->execute([$username]);
        $user = $stmt->fetch();
        
        if ($user && password_verify($password, $user['password'])) {
            // Generate token (simplified - use proper JWT in production)
            $token = base64_encode($user['id']);
            
            return $this->success([
                'token' => $token,
                'user' => [
                    'id' => $user['id'],
                    'username' => $user['username'],
                    'email' => $user['email'],
                    'role' => $user['role']
                ]
            ]);
        }
        
        return $this->error('Invalid credentials', 401);
    }
    
    private function handleProjects() {
        $id = $this->segments[1] ?? null;
        
        switch ($this->method) {
            case 'GET':
                return $id ? $this->getProject($id) : $this->getProjects();
            case 'POST':
                return $this->createProject();
            case 'PUT':
                return $this->updateProject($id);
            case 'DELETE':
                return $this->deleteProject($id);
            default:
                return $this->error('Method not allowed', 405);
        }
    }
    
    private function getProjects() {
        $type = $_GET['type'] ?? '';
        $limit = (int)($_GET['limit'] ?? 20);
        $offset = (int)($_GET['offset'] ?? 0);
        
        $sql = "SELECT * FROM projects";
        $params = [];
        
        if ($type) {
            $sql .= " WHERE project_type = ?";
            $params[] = $type;
        }
        
        $sql .= " ORDER BY created_at DESC LIMIT ? OFFSET ?";
        $params[] = $limit;
        $params[] = $offset;
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        $projects = $stmt->fetchAll();
        
        return $this->success($projects);
    }
    
    private function getProject($id) {
        $stmt = $this->db->prepare("SELECT * FROM projects WHERE id = ?");
        $stmt->execute([$id]);
        $project = $stmt->fetch();
        
        if (!$project) {
            return $this->error('Project not found', 404);
        }
        
        return $this->success($project);
    }
    
    private function createProject() {
        $input = json_decode(file_get_contents('php://input'), true);
        
        $required = ['title', 'description'];
        foreach ($required as $field) {
            if (empty($input[$field])) {
                return $this->error("Field '$field' is required", 400);
            }
        }
        
        $slug = generate_slug($input['title']);
        
        $stmt = $this->db->prepare("
            INSERT INTO projects (title, slug, description, short_description, client_name, location, project_type, start_date, end_date, status, is_featured)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            $input['title'],
            $slug,
            $input['description'],
            $input['short_description'] ?? '',
            $input['client_name'] ?? '',
            $input['location'] ?? '',
            $input['project_type'] ?? 'completed',
            $input['start_date'] ?? null,
            $input['end_date'] ?? null,
            $input['status'] ?? 'completed',
            $input['is_featured'] ?? false
        ]);
        
        $projectId = $this->db->lastInsertId();
        
        return $this->success(['id' => $projectId, 'message' => 'Project created successfully']);
    }
    
    private function updateProject($id) {
        if (!$id) {
            return $this->error('Project ID required', 400);
        }
        
        $input = json_decode(file_get_contents('php://input'), true);
        
        $stmt = $this->db->prepare("SELECT id FROM projects WHERE id = ?");
        $stmt->execute([$id]);
        if (!$stmt->fetch()) {
            return $this->error('Project not found', 404);
        }
        
        $fields = [];
        $params = [];
        
        $allowedFields = ['title', 'description', 'short_description', 'client_name', 'location', 'project_type', 'start_date', 'end_date', 'status', 'is_featured'];
        
        foreach ($allowedFields as $field) {
            if (isset($input[$field])) {
                $fields[] = "$field = ?";
                $params[] = $input[$field];
            }
        }
        
        if (empty($fields)) {
            return $this->error('No fields to update', 400);
        }
        
        if (isset($input['title'])) {
            $fields[] = "slug = ?";
            $params[] = generate_slug($input['title']);
        }
        
        $params[] = $id;
        
        $sql = "UPDATE projects SET " . implode(', ', $fields) . " WHERE id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        
        return $this->success(['message' => 'Project updated successfully']);
    }
    
    private function deleteProject($id) {
        if (!$id) {
            return $this->error('Project ID required', 400);
        }
        
        $stmt = $this->db->prepare("DELETE FROM projects WHERE id = ?");
        $stmt->execute([$id]);
        
        if ($stmt->rowCount() === 0) {
            return $this->error('Project not found', 404);
        }
        
        return $this->success(['message' => 'Project deleted successfully']);
    }
    
    private function handleServices() {
        $id = $this->segments[1] ?? null;
        
        switch ($this->method) {
            case 'GET':
                return $id ? $this->getService($id) : $this->getServices();
            case 'POST':
                return $this->createService();
            case 'PUT':
                return $this->updateService($id);
            case 'DELETE':
                return $this->deleteService($id);
            default:
                return $this->error('Method not allowed', 405);
        }
    }
    
    private function getServices() {
        $stmt = $this->db->prepare("SELECT * FROM services ORDER BY sort_order");
        $stmt->execute();
        $services = $stmt->fetchAll();
        
        return $this->success($services);
    }
    
    private function handleContacts() {
        if ($this->method === 'GET') {
            return $this->getContacts();
        }
        return $this->error('Method not allowed', 405);
    }
    
    private function getContacts() {
        $status = $_GET['status'] ?? '';
        $limit = (int)($_GET['limit'] ?? 20);
        $offset = (int)($_GET['offset'] ?? 0);
        
        $sql = "SELECT * FROM contact_submissions";
        $params = [];
        
        if ($status) {
            $sql .= " WHERE status = ?";
            $params[] = $status;
        }
        
        $sql .= " ORDER BY created_at DESC LIMIT ? OFFSET ?";
        $params[] = $limit;
        $params[] = $offset;
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        $contacts = $stmt->fetchAll();
        
        return $this->success($contacts);
    }
    
    private function handleAnalytics() {
        if ($this->method === 'GET') {
            return $this->getAnalytics();
        }
        return $this->error('Method not allowed', 405);
    }
    
    private function getAnalytics() {
        $days = (int)($_GET['days'] ?? 30);
        
        $stmt = $this->db->prepare("
            SELECT 
                visit_date,
                COUNT(*) as visits 
            FROM analytics 
            WHERE visit_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
            GROUP BY visit_date 
            ORDER BY visit_date
        ");
        $stmt->execute([$days]);
        $analytics = $stmt->fetchAll();
        
        return $this->success($analytics);
    }
    
    private function success($data, $code = 200) {
        http_response_code($code);
        echo json_encode([
            'success' => true,
            'data' => $data,
            'timestamp' => date('c')
        ]);
        exit();
    }
    
    private function error($message, $code = 400) {
        http_response_code($code);
        echo json_encode([
            'success' => false,
            'error' => $message,
            'timestamp' => date('c')
        ]);
        exit();
    }
}

// Initialize and route the request
$router = new APIRouter($db, $method, $segments);
$router->route();
?>
